import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { GalaxyNavControlService } from '@vendasta/galaxy/nav';
import { GalaxyPageModule, GalaxyPageOptions, GALAXY_PAGE_OPTIONS } from '@vendasta/galaxy/page';
import { CoreSharedModule } from '@vendasta/shared';
import { UIKitModule } from '@vendasta/uikit';
import { ChartComponent } from './charting/chart.component';
import { DisconnectServiceModalComponent } from './disconnect-service-modal/disconnect-service-modal.component';
import { GoogleSocialTokenBrokenComponent } from './google-social-token-broken/google-social-token-broken.component';
import { NavbarComponent } from './navbar.component';
import { PageComponent } from './page.component';
import { UpgradeCTADialogService } from './upgrade-cta-dialog/upgrade-cta-dialog.service';
import { MatButtonModule } from '@angular/material/button';
import { InfoBannerComponent } from './info-banner/info-banner.component';
import { GalaxyDatepickerModule } from '@vendasta/galaxy/datepicker';
import { StatTabsComponent } from './stat-tabs/stat-tabs.component';
import { DeltaComponent } from './delta/delta.component';
import { MatTabsModule } from '@angular/material/tabs';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { ColorIconComponent } from './color-icon/color-icon.component';
import { GoogleInsightsFiltersComponent } from '../google-insights/filters/google-insights-filters.component';
import { NgApexchartsModule } from 'ng-apexcharts';

function galaxyNavOptionsFactory(navCtrl: GalaxyNavControlService): GalaxyPageOptions {
  return {
    showToggle: true,
    toggleNav: () => navCtrl.toggle(),
  };
}

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatDatepickerModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatRadioModule,
    NgxChartsModule,
    TranslateModule,
    UIKitModule,
    CoreSharedModule,
    GalaxyPageModule,
    MatButtonModule,
    GalaxyDatepickerModule,
    DeltaComponent,
    MatTabsModule,
    GalaxyTooltipModule,
    NgApexchartsModule,
  ],
  declarations: [
    ChartComponent,
    DisconnectServiceModalComponent,
    GoogleSocialTokenBrokenComponent,
    PageComponent,
    NavbarComponent,
    InfoBannerComponent,
    StatTabsComponent,
    ColorIconComponent,
    GoogleInsightsFiltersComponent,
  ],
  exports: [
    ChartComponent,
    DisconnectServiceModalComponent,
    GoogleSocialTokenBrokenComponent,
    PageComponent,
    NavbarComponent,
    InfoBannerComponent,
    StatTabsComponent,
    ColorIconComponent,
    TranslateModule,
    RouterModule,
    MatIconModule,
  ],
  providers: [
    UpgradeCTADialogService,
    {
      provide: GALAXY_PAGE_OPTIONS,
      useFactory: galaxyNavOptionsFactory,
      deps: [GalaxyNavControlService],
    },
  ],
})
export class SharedModule {}
