import { NgModule } from '@angular/core';
import { BingInsightsComponent } from './bing-insights.component';
import { BingInsightsChartsComponent } from './bing-insights-charts/bing-insights-charts.component';
import { ViewCardComponent } from './view-card/view-card.component';
import { BingInsightsRouting } from './bing-insights.routing';
import { Mat<PERSON>ard, MatCardContent, MatCardHeader, MatCardSubtitle, MatCardTitle } from '@angular/material/card';
import { NgApexchartsModule } from 'ng-apexcharts';
import { AsyncPipe, NgStyle } from '@angular/common';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SharedModule } from '../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleInsightsModule } from '../google-insights/google-insights.module';
import { BingEmptyStatesComponent } from './bing-empty-states/bing-empty-states.component';

@NgModule({
  declarations: [BingInsightsComponent, BingInsightsChartsComponent, ViewCardComponent],
  imports: [
    BingInsightsRouting,
    MatCard,
    MatCardHeader,
    MatCardContent,
    NgApexchartsModule,
    NgStyle,
    MatProgressSpinner,
    AsyncPipe,
    GalaxyAlertModule,
    GalaxyLoadingSpinnerModule,
    SharedModule,
    TranslateModule,
    MatCardTitle,
    MatCardSubtitle,
    GoogleInsightsModule,
    BingEmptyStatesComponent,
  ],
  exports: [BingInsightsComponent, BingEmptyStatesComponent],
})
export class BingInsightsModule {}
