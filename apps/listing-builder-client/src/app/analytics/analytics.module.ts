import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { AnalyticsComponent } from './analytics.component';
import { AnalyticsRouting } from './analytics.routing';
import { PageComponent } from '../shared/page.component';
import { MatTabNavBar, MatTabLink, MatTabNavPanel } from '@angular/material/tabs';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

@NgModule({
  declarations: [AnalyticsComponent],
  imports: [
    CommonModule,
    AnalyticsRouting,
    SharedModule,
    MatTabNavBar,
    MatTabLink,
    MatTabNavPanel,
    GalaxyLoadingSpinnerModule,
    MatTooltipModule,
  ],
  exports: [AnalyticsComponent],
})
export class AnalyticsModule {}
