import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AnalyticsComponent } from './analytics.component';
import { AnalyticsRouting } from './analytics.routing';
import { SharedModule } from '../shared/shared.module';
import { MatTabNav, MatTabLink, MatTabNavPanel } from '@angular/material/tabs';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

@NgModule({
  declarations: [AnalyticsComponent],
  imports: [
    CommonModule,
    AnalyticsRouting,
    SharedModule,
    MatTabNav,
    MatTabLink,
    MatTabNavPanel,
    GalaxyLoadingSpinnerModule,
    MatTooltipModule,
  ],
  exports: [AnalyticsComponent],
})
export class AnalyticsModule {}
