import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AnalyticsComponent } from './analytics.component';

const routes: Routes = [
  {
    path: '',
    component: AnalyticsComponent,
    children: [
      {
        path: '',
        redirectTo: 'google',
        pathMatch: 'full',
      },
      {
        path: 'google',
        loadChildren: () => import('../google-insights/google-insights.module').then((m) => m.GoogleInsightsModule),
      },
      {
        path: 'bing',
        loadChildren: () => import('../bing-insights/bing-insights.module').then((m) => m.BingInsightsModule),
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AnalyticsRouting {}
