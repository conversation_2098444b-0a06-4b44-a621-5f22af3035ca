<app-page [pageTitle]="pageTitleKey$ | async | translate">
  <div class="tab-nav-container">
    <nav
      mat-tab-nav-bar
      mat-stretch-tabs="false"
      mat-align-tabs="start"
      [tabPanel]="tabPanel"
    >
      <a
        mat-tab-link
        class="tab-nav"
        [routerLink]="['./google']"
        [active]="isActiveTabGoogle()"
      >
        <span class="tab-label-spacer"></span>
        <img [src]="googleIconUrl" alt="Google Icon" width="24" height="24" class="tab-icon" />
        <span class="tab-label-text">{{ 'GOOGLE_INSIGHTS.TITLE' | translate }}</span>
        <span class="tab-label-extra-spacer"></span>
      </a>
      <a
        mat-tab-link
        class="tab-nav"
        [routerLink]="['./bing']"
        [active]="isActiveTabBing()"
        [class.disabled]="(upgradeToProEnabled$ | async) === false"
        [matTooltip]="(upgradeToProEnabled$ | async) === false ? ('BING_INSIGHTS.UPGRADE_TOOLTIP' | translate) : ''"
        (click)="onBingTabClick($event)"
      >
        <span class="tab-label-spacer"></span>
        <img [src]="bingIconUrl" alt="Bing Icon" width="24" height="24" class="tab-icon" />
        <span class="tab-label-text">{{ 'BING_INSIGHTS.TITLE' | translate }}</span>
        <span class="tab-label-extra-spacer"></span>
      </a>
    </nav>
    <mat-tab-nav-panel #tabPanel></mat-tab-nav-panel>
  </div>
  <br />
  <router-outlet></router-outlet>
</app-page>
